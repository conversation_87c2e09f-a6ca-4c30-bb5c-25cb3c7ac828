{"version": 3, "sources": ["../../@mui/material/esm/ImageListItem/ImageListItem.js", "../../@mui/material/esm/ImageListItem/imageListItemClasses.js"], "sourcesContent": ["'use client';\n\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport ImageListContext from \"../ImageList/ImageListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport imageListItemClasses, { getImageListItemUtilityClass } from \"./imageListItemClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant],\n    img: ['img']\n  };\n  return composeClasses(slots, getImageListItemUtilityClass, classes);\n};\nconst ImageListItemRoot = styled('li', {\n  name: 'MuiImageListItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${imageListItemClasses.img}`]: styles.img\n    }, styles.root, styles[ownerState.variant]];\n  }\n})({\n  display: 'block',\n  position: 'relative',\n  [`& .${imageListItemClasses.img}`]: {\n    objectFit: 'cover',\n    width: '100%',\n    height: '100%',\n    display: 'block'\n  },\n  variants: [{\n    props: {\n      variant: 'standard'\n    },\n    style: {\n      // For titlebar under list item\n      display: 'flex',\n      flexDirection: 'column'\n    }\n  }, {\n    props: {\n      variant: 'woven'\n    },\n    style: {\n      height: '100%',\n      alignSelf: 'center',\n      '&:nth-of-type(even)': {\n        height: '70%'\n      }\n    }\n  }, {\n    props: {\n      variant: 'standard'\n    },\n    style: {\n      [`& .${imageListItemClasses.img}`]: {\n        height: 'auto',\n        flexGrow: 1\n      }\n    }\n  }]\n});\nconst ImageListItem = /*#__PURE__*/React.forwardRef(function ImageListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiImageListItem'\n  });\n\n  // TODO: - Use jsdoc @default?: \"cols rows default values are for docs only\"\n  const {\n    children,\n    className,\n    cols = 1,\n    component = 'li',\n    rows = 1,\n    style,\n    ...other\n  } = props;\n  const {\n    rowHeight = 'auto',\n    gap,\n    variant\n  } = React.useContext(ImageListContext);\n  let height = 'auto';\n  if (variant === 'woven') {\n    height = undefined;\n  } else if (rowHeight !== 'auto') {\n    height = rowHeight * rows + gap * (rows - 1);\n  }\n  const ownerState = {\n    ...props,\n    cols,\n    component,\n    gap,\n    rowHeight,\n    rows,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListItemRoot, {\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: {\n      height,\n      gridColumnEnd: variant !== 'masonry' ? `span ${cols}` : undefined,\n      gridRowEnd: variant !== 'masonry' ? `span ${rows}` : undefined,\n      marginBottom: variant === 'masonry' ? gap : undefined,\n      breakInside: variant === 'masonry' ? 'avoid' : undefined,\n      ...style\n    },\n    ownerState: ownerState,\n    ...other,\n    children: React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The ImageListItem component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      if (child.type === 'img' || isMuiElement(child, ['Image'])) {\n        return /*#__PURE__*/React.cloneElement(child, {\n          className: clsx(classes.img, child.props.className)\n        });\n      }\n      return child;\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `<img>`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Width of the item in number of grid columns.\n   * @default 1\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the item in number of grid rows.\n   * @default 1\n   */\n  rows: integerPropType,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ImageListItem;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getImageListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiImageListItem', slot);\n}\nconst imageListItemClasses = generateUtilityClasses('MuiImageListItem', ['root', 'img', 'standard', 'woven', 'masonry', 'quilted']);\nexport default imageListItemClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,wBAAsB;AACtB,YAAuB;AACvB,sBAA2B;;;ACLpB,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,oBAAoB,IAAI;AACtD;AACA,IAAM,uBAAuB,uBAAuB,oBAAoB,CAAC,QAAQ,OAAO,YAAY,SAAS,WAAW,SAAS,CAAC;AAClI,IAAO,+BAAQ;;;ADOf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,OAAO;AAAA,IACtB,KAAK,CAAC,KAAK;AAAA,EACb;AACA,SAAO,eAAe,OAAO,8BAA8B,OAAO;AACpE;AACA,IAAM,oBAAoB,eAAO,MAAM;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,6BAAqB,GAAG,EAAE,GAAG,OAAO;AAAA,IAC7C,GAAG,OAAO,MAAM,OAAO,WAAW,OAAO,CAAC;AAAA,EAC5C;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,CAAC,MAAM,6BAAqB,GAAG,EAAE,GAAG;AAAA,IAClC,WAAW;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA;AAAA,MAEL,SAAS;AAAA,MACT,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,uBAAuB;AAAA,QACrB,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,CAAC,MAAM,6BAAqB,GAAG,EAAE,GAAG;AAAA,QAClC,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,gBAAmC,iBAAW,SAASA,eAAc,SAAS,KAAK;AACvF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AAGD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAU,iBAAW,wBAAgB;AACrC,MAAI,SAAS;AACb,MAAI,YAAY,SAAS;AACvB,aAAS;AAAA,EACX,WAAW,cAAc,QAAQ;AAC/B,aAAS,YAAY,OAAO,OAAO,OAAO;AAAA,EAC5C;AACA,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,mBAAmB;AAAA,IAC1C,IAAI;AAAA,IACJ,WAAW,aAAK,QAAQ,MAAM,QAAQ,OAAO,GAAG,SAAS;AAAA,IACzD;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA,eAAe,YAAY,YAAY,QAAQ,IAAI,KAAK;AAAA,MACxD,YAAY,YAAY,YAAY,QAAQ,IAAI,KAAK;AAAA,MACrD,cAAc,YAAY,YAAY,MAAM;AAAA,MAC5C,aAAa,YAAY,YAAY,UAAU;AAAA,MAC/C,GAAG;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,UAAgB,eAAS,IAAI,UAAU,WAAS;AAC9C,UAAI,CAAqB,qBAAe,KAAK,GAAG;AAC9C,eAAO;AAAA,MACT;AACA,UAAI,MAAuC;AACzC,gBAAI,4BAAW,KAAK,GAAG;AACrB,kBAAQ,MAAM,CAAC,0EAA0E,sCAAsC,EAAE,KAAK,IAAI,CAAC;AAAA,QAC7I;AAAA,MACF;AACA,UAAI,MAAM,SAAS,SAAS,qBAAa,OAAO,CAAC,OAAO,CAAC,GAAG;AAC1D,eAA0B,mBAAa,OAAO;AAAA,UAC5C,WAAW,aAAK,QAAQ,KAAK,MAAM,MAAM,SAAS;AAAA,QACpD,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,cAAc,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,wBAAQ;", "names": ["ImageListItem", "_jsx", "PropTypes"]}